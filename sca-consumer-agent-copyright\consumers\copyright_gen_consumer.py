import time
from typing import Dict, Any

from common_config.common_config import RedisKeyConfig
from omni.msg_queue.redis_set_consumer import consume_redis_set
from models.models import CopyrightTask
from agent.graph import graph
from omni.log.log import olog


@consume_redis_set(redis_key=RedisKeyConfig.COPYRIGHT_GEN_SET, num_tasks=10)
async def handle_task(message_data: Dict[str, Any]) -> None:
    """
    处理软件著作权生成任务

    Args:
        message_data: Redis消息数据，包含CopyrightTask的ID
    """
    task_id = None
    task = None

    try:
        # 1. 从message_data中提取CopyrightTask的ID
        task_id = message_data.get("task_id") or message_data.get("id")
        if not task_id:
            raise ValueError("message_data中未找到task_id或id字段")

        olog.info(f"开始处理软件著作权生成任务, task_id: {task_id}")

        # 2. 根据ID查询数据库获取CopyrightTask对象
        try:
            task = await CopyrightTask.get(task_id)
            if not task:
                raise ValueError(f"未找到ID为 {task_id} 的CopyrightTask")
        except Exception as e:
            olog.error(f"查询CopyrightTask失败, task_id: {task_id}, 错误: {str(e)}")
            raise

        if not task.name:
            raise ValueError(f"CopyrightTask的name字段为空, task_id: {task_id}")

        olog.info(f"获取到CopyrightTask, name: {task.name}, status: {task.status}")

        # 3. 使用task.name作为user_requirement参数运行graph
        try:
            olog.info(f"开始运行graph工作流, user_requirement: {task.name}")

            # 准备输入参数
            input_data = {"user_requirement": task.name}

            # 异步执行graph工作流
            workflow_result = await graph.ainvoke(input_data)

            olog.info(f"graph工作流执行成功, 结果: {workflow_result}")
        except Exception as e:
            olog.error(f"graph工作流执行失败, user_requirement: {task.name}, 错误: {str(e)}")
            raise

        # 4. 处理运行结果并更新数据库
        oss_key = workflow_result.get("oss_key")
        if not oss_key:
            raise ValueError("graph工作流执行结果中未找到oss_key字段")

        # 更新任务状态为已完成，并设置document_key
        try:
            task.status = "已完成"
            task.document_key = oss_key
            task.completed_at = int(time.time())
            await task.save()
            olog.info(f"更新CopyrightTask成功, task_id: {task.id}, status: 已完成, document_key: {oss_key}")
        except Exception as e:
            olog.error(f"更新CopyrightTask失败, task_id: {task.id}, 错误: {str(e)}")
            raise

        olog.info(f"软件著作权生成任务处理成功, task_id: {task_id}, oss_key: {oss_key}")

    except Exception as e:
        error_msg = f"软件著作权生成任务处理失败, task_id: {task_id}, 错误: {str(e)}"
        olog.error(error_msg)

        # 如果已经获取到task对象，更新状态为生成失败
        if task:
            try:
                task.status = "生成失败"
                await task.save()
                olog.info(f"已更新任务状态为生成失败, task_id: {task_id}")
            except Exception as update_error:
                olog.error(f"更新任务状态失败, task_id: {task_id}, 错误: {str(update_error)}")

        # 重新抛出异常，让消费者框架处理
        raise
